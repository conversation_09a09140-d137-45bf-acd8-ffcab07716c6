# <PERSON>
**Senior Staff Engineer | Data Platform & Infrastructure Architecture**

📧 [Your Email] | 📱 [Your Phone] | 🌐 [LinkedIn] | 📍 [Location]

---

## Executive Summary

Senior Staff Engineer with **17+ years** leading company-wide infrastructure initiatives and architecting distributed data platforms at scale. Proven track record of defining technical roadmaps spanning **hundreds of engineers across multiple organizations**, driving business-critical outcomes, and mentoring senior technical talent.

**Leadership & Impact:**
- **Organizational Leadership**: Led cross-functional initiatives spanning 30+ engineers across Infrastructure, Data Platform, Product Engineering, and Operations teams
- **Technical Roadmap Ownership**: Defined and executed multi-year architectural strategies for data processing systems handling **10+ billion events monthly**
- **Business Outcomes**: Delivered systems contributing **$100M+ annual transaction volume** and **4% company revenue** with measurable performance improvements
- **Mentorship & Culture**: Mentored 15+ senior engineers and managers, setting technical excellence standards across organizations

**Technical Expertise:**
- **Infrastructure & Data Platforms**: Event-driven architecture, real-time streaming, distributed systems, performance optimization
- **Financial Systems**: Payment processing, regulatory compliance, real-time transaction systems, data security
- **Cross-Platform Integration**: API design, microservices architecture, third-party integrations, system reliability

---

## Professional Experience

### **Juniper Square** | Remote
**Staff Software Engineer - Data Platform Architecture & Technical Leadership**
*November 2023 – Present*

- **Defined and owned technical roadmap** for unified data integration platform serving **10+ billion events monthly** across financial services infrastructure, spanning Data Platform, Product Engineering, and Operations teams
- **Led cross-organizational initiative** involving 15+ engineers across platform, mobile, and analytics teams to architect event-driven data architecture using **Apache Kafka** and distributed streaming systems
- **Drove company-wide performance optimization program**, delivering **30-90% improvement in P95/P99 API response times** and achieving **99.9% system uptime** for business-critical financial data processing
- **Established technical standards and best practices** for data modeling, event sourcing, and distributed systems design, influencing architectural decisions across multiple product teams
- **Mentored senior engineers and technical leads** on system architecture, performance optimization, and technical decision-making, contributing to team growth and technical excellence culture

### **CS Disco** | Remote
**Software Architect - Enterprise Data Platform & Cross-Organizational Leadership**
*August 2021 – May 2023*

- **Led technical roadmap definition** for enterprise data warehouse spanning **25+ engineers across Infrastructure, Data Platform, and Product Engineering** organizations with direct impact on revenue platform architecture
- **Drove cross-functional initiative** to architect and deliver Limited Global Admin system, coordinating with Security, Compliance, and Operations teams to deliver MVP in **<2 months** with enterprise-grade audit capabilities
- **Established integration standards** across organization, implementing production-grade connections with Salesforce, Dialpad, and Seismic using **OAuth 2.0**, webhook processing, and real-time synchronization patterns
- **Architected enterprise ETL infrastructure** to **Snowflake**, processing petabytes of legal document metadata and user analytics, enabling data-driven decision making for Fortune 500 clients
- **Designed microservices architecture standards** supporting 100+ services with comprehensive API gateway, rate limiting, authentication, and observability frameworks

### **Momentive-AI (SurveyMonkey)** | Ottawa  
**Staff Software Engineer - Platform & Data Systems**  
*May 2020 – August 2021*

- **Led development of multi-million GMV Brand Tracking platform** from concept to GA in **9 months**, processing survey data and market research analytics at scale
- **Directed cross-functional team of 30+** across engineering, QA, and product to scale market research data platform supporting Fortune 500 clients
- **Delivered company-wide rebrand initiative** in **2 months**, migrating all public-facing systems with zero downtime and automated deployment pipelines
- **Implemented security vulnerability remediation** through Bug Bounty Program participation, strengthening data protection and compliance posture

### **Veem** | Ottawa  
**Senior Software Engineer, Lead - Financial Data Platform**  
*February 2019 – May 2020*

- **Led 15+ person team** building B2B payment processing and funds movement platform handling **$100M+ annual transaction volume**
- **Launched Veem Wallet product**, contributing **4% of company revenue** in Q1 2020 with **<1% user adoption**, demonstrating high-value feature delivery
- **Architected real-time payment integrations** with global financial institutions including **HSBC, SVB, Fidor**, implementing secure API connections and regulatory compliance
- **Built third-party data integrations** with **Plaid, Trulioo, Coins.ph** for identity verification, account linking, and international payment processing
- **Designed event-driven transaction processing** with real-time fraud detection, automated reconciliation, and comprehensive audit trails

### **Cisco Systems** | Ottawa  
**Senior Software Development Engineer - Network Automation Platform**  
*February 2017 – February 2019*

- **Co-led global team of 20+ engineers** and 40 operations staff delivering network automation platform for enterprise infrastructure management
- **Served as technical lead** for XPRESSO dashboard and core contributor to **PyATS** and **Genie** open-source telemetry frameworks
- **Built distributed data collection systems** processing network telemetry from 10,000+ devices with real-time monitoring and alerting

### **Nokia** | Ottawa  
**Software Development Engineer - Enterprise Network Management**  
*November 2011 – February 2017*

- **Developed core modules** for **5620 SAM** network management platform, focusing on performance optimization and system reliability
- **Implemented data processing pipelines** for network performance metrics, fault management, and configuration automation

### **N-able** | Ottawa  
**Intermediate Software Developer - Platform Integration**  
*July 2008 – November 2011*

- **Owned critical modules** in **N-central** platform: data migration, system monitoring, report generation, and third-party API integration
- **Discovered and mitigated security vulnerabilities** including XSS and path traversal, strengthening platform security posture

---

## Technical Expertise

### **Programming Languages**
Python, Java, Kotlin, JavaScript, Scala, Go

### **Data & Analytics Platforms**  
Apache Kafka, Apache Spark, Snowflake, Elasticsearch, Redis, PostgreSQL, DynamoDB, Kinesis, Apache Flink

### **Cloud & Infrastructure**
AWS (EC2, ECS, Lambda, S3, RDS, SNS, SQS, API Gateway, Step Functions), Docker, Terraform, Kubernetes

### **Integration & APIs**
REST APIs, GraphQL, OAuth 2.0, Webhook processing, Event-driven architecture, Microservices, API Gateway

### **Data Engineering**
ETL/ELT pipelines, Real-time streaming, Event sourcing, Data modeling, Schema evolution, Data governance

### **Frameworks & Tools**
Spring Boot, Django, React, Angular, Datadog, Looker, CI/CD, Infrastructure as Code

---

## Recognition & Leadership

- **Speaker** - SurveyMonkey 2021 Annual Architecture Conference  
- **Cisco Pioneer Award 2018** - DevX Productivity (PyATS/Genie platform)  
- **N-able Peer Awards** - Self-Reliance & Productivity (2009, 2010)

---

## Education

**Bachelor of Science in Computer Science**  
Carleton University | Ottawa, Ontario
