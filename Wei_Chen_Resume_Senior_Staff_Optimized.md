# <PERSON>
**Senior Staff Engineer | Data Platform & Infrastructure Architecture**

📧 [Your Email] | 📱 [Your Phone] | 🌐 [LinkedIn] | 📍 [Location]

---

## Executive Summary

Senior Staff Engineer with **17+ years** leading company-wide infrastructure initiatives and architecting distributed data platforms at scale. Proven track record of defining technical roadmaps spanning **hundreds of engineers across multiple organizations**, driving business-critical outcomes, and mentoring senior technical talent.

**Leadership & Organizational Impact:**
- **Cross-Organizational Leadership**: Led initiatives spanning 30+ engineers across Infrastructure, Data Platform, Product Engineering, and Operations teams
- **Technical Roadmap Ownership**: Defined and executed multi-year architectural strategies for systems processing **10+ billion events monthly**
- **Business Outcomes**: Delivered platforms contributing **$100M+ annual transaction volume** and **4% company revenue** with measurable performance improvements
- **Mentorship & Culture**: Mentored 15+ senior engineers and managers, setting technical excellence standards and driving organizational growth

**Technical Expertise:**
- **Infrastructure & Data Platforms**: Event-driven architecture, real-time streaming, distributed systems, performance optimization at scale
- **Financial Systems**: Payment processing, regulatory compliance, real-time transaction systems, enterprise data security
- **Cross-Platform Integration**: API design, microservices architecture, third-party integrations, system reliability engineering

---

## Professional Experience

### **Juniper Square** | Remote
**Staff Software Engineer - Data Platform Architecture & Technical Leadership**  
*November 2023 – Present*

- **Defined and owned technical roadmap** for unified data integration platform serving **10+ billion events monthly** across financial services infrastructure, coordinating with Data Platform, Product Engineering, and Operations organizations
- **Led cross-organizational initiative** involving 15+ engineers across platform, mobile, and analytics teams to architect event-driven data systems using **Apache Kafka** and distributed streaming technologies
- **Drove company-wide performance optimization program**, delivering **30-90% improvement in P95/P99 API response times** and achieving **99.9% system uptime** for business-critical financial data processing
- **Established technical standards and architectural patterns** for data modeling, event sourcing, and distributed systems design, influencing technical decisions across multiple product teams
- **Mentored senior engineers and technical leads** on system architecture, performance optimization, and technical decision-making while contributing to organizational technical excellence culture

### **CS Disco** | Remote  
**Software Architect - Enterprise Data Platform & Cross-Organizational Leadership**  
*August 2021 – May 2023*

- **Led technical roadmap definition** for enterprise data warehouse spanning **25+ engineers across Infrastructure, Data Platform, and Product Engineering** organizations with direct revenue platform impact
- **Drove cross-functional initiative** to architect Limited Global Admin system, coordinating with Security, Compliance, and Operations teams to deliver enterprise-grade MVP in **<2 months**
- **Established integration standards** across organization, implementing production-grade connections with Salesforce, Dialpad, and Seismic using **OAuth 2.0**, webhook processing, and real-time synchronization
- **Architected enterprise ETL infrastructure** to **Snowflake**, processing petabytes of legal document metadata and user analytics, enabling data-driven decision making for Fortune 500 clients
- **Designed microservices architecture standards** supporting 100+ services with comprehensive API gateway, rate limiting, authentication, and observability frameworks

### **Momentive-AI (SurveyMonkey)** | Ottawa  
**Staff Software Engineer - Platform Architecture & Organizational Leadership**  
*May 2020 – August 2021*

- **Led cross-functional initiative** for multi-million GMV Brand Tracking platform, directing **30+ person team** across Engineering, QA, and Product organizations from concept to GA in **9 months**
- **Drove company-wide infrastructure modernization**, delivering complete rebrand initiative in **2 months** with zero downtime across all public-facing systems and automated deployment pipelines
- **Established security and compliance standards** through Bug Bounty Program leadership, strengthening data protection posture and organizational security practices
- **Architected market research data platform** supporting Fortune 500 clients with real-time analytics, survey processing, and enterprise-grade reporting capabilities

### **Veem** | Ottawa  
**Senior Software Engineer, Lead - Financial Platform Architecture**  
*February 2019 – May 2020*

- **Led technical and organizational strategy** for 15+ person team building B2B payment processing platform handling **$100M+ annual transaction volume**
- **Drove product innovation initiative**, launching Veem Wallet contributing **4% of company revenue** in Q1 2020 with **<1% user adoption**, demonstrating high-impact feature delivery
- **Architected real-time payment integrations** with global financial institutions including **HSBC, SVB, Fidor**, implementing secure API connections and regulatory compliance frameworks
- **Built enterprise-grade third-party integrations** with **Plaid, Trulioo, Coins.ph** for identity verification, account linking, and international payment processing
- **Designed event-driven transaction processing** with real-time fraud detection, automated reconciliation, and comprehensive audit trails for financial compliance

### **Cisco Systems** | Ottawa  
**Senior Software Development Engineer - Global Platform Leadership**  
*February 2017 – February 2019*

- **Co-led global technical initiative** spanning **20+ engineers and 40 operations staff** across multiple geographic regions for network automation platform delivery
- **Served as technical lead** for XPRESSO dashboard and core contributor to **PyATS** and **Genie** open-source telemetry frameworks, influencing industry standards
- **Built distributed data collection systems** processing network telemetry from 10,000+ enterprise devices with real-time monitoring, alerting, and automated remediation

### **Nokia** | Ottawa  
**Software Development Engineer - Enterprise Platform Development**  
*November 2011 – February 2017*

- **Developed core infrastructure modules** for **5620 SAM** network management platform, focusing on performance optimization and enterprise-grade system reliability
- **Implemented large-scale data processing pipelines** for network performance metrics, fault management, and configuration automation across enterprise deployments

### **N-able** | Ottawa  
**Intermediate Software Developer - Platform Integration & Security**  
*July 2008 – November 2011*

- **Owned critical platform modules** in **N-central**: data migration, system monitoring, report generation, and third-party API integration for enterprise clients
- **Led security vulnerability remediation**, discovering and mitigating XSS and path traversal vulnerabilities, strengthening platform security posture

---

## Technical Expertise

### **Programming Languages & Frameworks**
Python, Java, Kotlin, JavaScript, Scala, Go, Spring Boot, Django, React, Angular

### **Data & Analytics Infrastructure**  
Apache Kafka, Apache Spark, Apache Flink, Snowflake, Elasticsearch, Redis, PostgreSQL, DynamoDB, Kinesis, Pinot, Trino

### **Cloud & Infrastructure Platforms**
AWS (EC2, ECS, Lambda, S3, RDS, SNS, SQS, API Gateway, Step Functions), Docker, Kubernetes, Terraform, Infrastructure as Code

### **Integration & API Architecture**
REST APIs, GraphQL, OAuth 2.0, Webhook processing, Event-driven architecture, Microservices, API Gateway, Service mesh

### **Data Engineering & Processing**
ETL/ELT pipelines, Real-time streaming, Event sourcing, Data modeling, Schema evolution, Data governance, Distributed systems

### **Observability & Operations**
Datadog, Looker, System monitoring, Performance optimization, CI/CD, Site reliability engineering

---

## Recognition & Leadership

- **Speaker** - SurveyMonkey 2021 Annual Architecture Conference (Technical Leadership)
- **Cisco Pioneer Award 2018** - DevX Productivity (PyATS/Genie open-source platform)  
- **N-able Peer Awards** - Self-Reliance & Productivity (2009, 2010)

---

## Education

**Bachelor of Science in Computer Science**  
Carleton University | Ottawa, Ontario
