#!/bin/bash

# Google Cloud Operations Agent Deployment Script for Bittensor Logs
# This script installs and configures the Google Cloud Operations Agent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"
BACKUP_CONFIG="/etc/google-cloud-ops-agent/config.yaml.backup"
LOG_DIR="/var/log/bittensor"
SERVICE_NAME="google-cloud-ops-agent"

echo -e "${BLUE}Google Cloud Operations Agent Setup for Bittensor Logs${NC}"
echo "========================================================"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root${NC}"
   exit 1
fi

# Check if Google Cloud Operations Agent is installed
check_ops_agent() {
    if systemctl list-units --full -all | grep -Fq "$SERVICE_NAME"; then
        echo -e "${GREEN}Google Cloud Operations Agent is already installed${NC}"
        return 0
    else
        echo -e "${YELLOW}Google Cloud Operations Agent is not installed${NC}"
        return 1
    fi
}

# Install Google Cloud Operations Agent
install_ops_agent() {
    echo -e "${BLUE}Installing Google Cloud Operations Agent...${NC}"
    
    # Download and run the installation script
    curl -sSO https://dl.google.com/cloudagents/add-google-cloud-ops-agent-repo.sh
    sudo bash add-google-cloud-ops-agent-repo.sh --also-install
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Google Cloud Operations Agent installed successfully${NC}"
    else
        echo -e "${RED}Failed to install Google Cloud Operations Agent${NC}"
        exit 1
    fi
}

# Create log directory
create_log_directory() {
    echo -e "${BLUE}Creating log directory...${NC}"
    
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        chmod 755 "$LOG_DIR"
        echo -e "${GREEN}Created log directory: $LOG_DIR${NC}"
    else
        echo -e "${YELLOW}Log directory already exists: $LOG_DIR${NC}"
    fi
}

# Backup existing configuration
backup_config() {
    if [ -f "$CONFIG_FILE" ]; then
        echo -e "${BLUE}Backing up existing configuration...${NC}"
        cp "$CONFIG_FILE" "$BACKUP_CONFIG"
        echo -e "${GREEN}Configuration backed up to: $BACKUP_CONFIG${NC}"
    fi
}

# Deploy configuration
deploy_config() {
    echo -e "${BLUE}Deploying Operations Agent configuration...${NC}"
    
    # Choose which config to use
    if [ -f "ops-agent-advanced-config.yaml" ]; then
        CONFIG_SOURCE="ops-agent-advanced-config.yaml"
    elif [ -f "ops-agent-config.yaml" ]; then
        CONFIG_SOURCE="ops-agent-config.yaml"
    else
        echo -e "${RED}No configuration file found!${NC}"
        exit 1
    fi
    
    # Copy configuration
    cp "$CONFIG_SOURCE" "$CONFIG_FILE"
    chmod 644 "$CONFIG_FILE"
    
    echo -e "${GREEN}Configuration deployed from: $CONFIG_SOURCE${NC}"
}

# Validate configuration
validate_config() {
    echo -e "${BLUE}Validating configuration...${NC}"
    
    # Check if the configuration file is valid YAML
    if command -v python3 &> /dev/null; then
        python3 -c "import yaml; yaml.safe_load(open('$CONFIG_FILE'))" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}Configuration file is valid YAML${NC}"
        else
            echo -e "${RED}Configuration file has YAML syntax errors${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}Python3 not available, skipping YAML validation${NC}"
    fi
}

# Restart service
restart_service() {
    echo -e "${BLUE}Restarting Google Cloud Operations Agent...${NC}"
    
    systemctl restart "$SERVICE_NAME"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Service restarted successfully${NC}"
    else
        echo -e "${RED}Failed to restart service${NC}"
        exit 1
    fi
    
    # Check service status
    sleep 5
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        echo -e "${GREEN}Service is running${NC}"
    else
        echo -e "${RED}Service is not running${NC}"
        systemctl status "$SERVICE_NAME"
        exit 1
    fi
}

# Show service status
show_status() {
    echo -e "${BLUE}Service Status:${NC}"
    systemctl status "$SERVICE_NAME" --no-pager
    
    echo -e "\n${BLUE}Recent Logs:${NC}"
    journalctl -u "$SERVICE_NAME" --no-pager -n 20
}

# Main execution
main() {
    echo -e "${BLUE}Starting deployment...${NC}"
    
    # Check if already installed
    if ! check_ops_agent; then
        install_ops_agent
    fi
    
    create_log_directory
    backup_config
    deploy_config
    validate_config
    restart_service
    show_status
    
    echo -e "\n${GREEN}Deployment completed successfully!${NC}"
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. Copy your log files to: $LOG_DIR"
    echo "2. Monitor logs with: journalctl -u $SERVICE_NAME -f"
    echo "3. Check Google Cloud Console for log entries"
    echo "4. Test with: python3 test_log_parser.py"
}

# Handle command line arguments
case "${1:-}" in
    "install")
        install_ops_agent
        ;;
    "config")
        deploy_config
        validate_config
        restart_service
        ;;
    "status")
        show_status
        ;;
    "test")
        python3 test_log_parser.py
        ;;
    *)
        main
        ;;
esac
