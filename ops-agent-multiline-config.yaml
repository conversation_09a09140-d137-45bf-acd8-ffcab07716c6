logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      # Multi-line configuration
      multiline:
        # Pattern that matches the start of a new log entry
        pattern: '\x1b\[[\d;]*m\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\x1b\[[\d;]*m \|'
        negate: false
        match: after
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      # Regex that handles both full lines and header-only lines
      # Now with multiline support using DOTALL flag equivalent
      regex: '\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| ?(?P<log_message>.*?)(?:\x1b\[[\d;]*m)*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
