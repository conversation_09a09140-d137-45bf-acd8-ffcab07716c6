# <PERSON>
**Senior Technical Leader | Cross-Organizational Leadership & Distributed Systems Architecture**

📧 [Your Email] | 📱 [Your Phone] | 🌐 [LinkedIn] | 📍 [Location]

---

## Executive Summary

**Senior Technical Leader** with **17+ years** driving cross-organizational initiatives and architecting distributed systems at scale. Proven track record of **leading technical roadmaps spanning 30+ engineers**, **mentoring senior technical talent**, and **delivering business-critical outcomes** in heavily regulated financial services environments.

**Key Leadership Capabilities:**
- **Cross-Organizational Leadership**: Led initiatives spanning **30+ engineers across Infrastructure, Data Platform, Product Engineering, and Operations**
- **Distributed Team Management**: Successfully managed **remote teams across time zones** with seamless coordination and knowledge transfer
- **Technical Strategy**: Defined **multi-year architectural roadmaps** for systems processing **$100M+ annual volume** and **10+ billion events monthly**
- **Talent Development**: Mentored **15+ senior engineers** and established **technical excellence standards** across multiple organizations
- **Business Impact**: Delivered **30-90% performance improvements**, **99.9% uptime**, and **$100M+ transaction volume** with **4% revenue contribution**

---

## Professional Experience

### **Juniper Square** | Remote | *November 2023 – Present*
**Staff Software Engineer - Data Platform Architecture & Cross-Organizational Leadership**

- **Led cross-organizational initiative** involving **15+ engineers across platform, mobile, and analytics teams** to architect unified data integration platform serving **10+ billion events monthly**
- **Defined technical roadmap** for event-driven data systems using **Apache Kafka**, establishing **technical standards** influencing decisions across multiple product teams
- **Drove performance optimization program** delivering **30-90% improvement in P95/P99 API response times** and **99.9% system uptime** for business-critical financial data processing
- **Mentored senior engineers** on system architecture and technical decision-making while contributing to **organizational technical excellence culture**

### **CS Disco** | Remote | *August 2021 – May 2023*
**Software Architect - Enterprise Data Platform & Cross-Organizational Leadership**

- **Led technical roadmap** for enterprise data warehouse spanning **25+ engineers across Infrastructure, Data Platform, and Product Engineering** with direct revenue impact
- **Drove cross-functional initiative** architecting Limited Global Admin system, coordinating with Security, Compliance, and Operations teams to deliver **enterprise MVP in <2 months**
- **Established integration standards** implementing production-grade connections with Salesforce, Dialpad, and Seismic using **OAuth 2.0** and real-time synchronization
- **Architected enterprise ETL infrastructure** to **Snowflake**, processing **petabytes of legal document metadata** enabling data-driven decision making for Fortune 500 clients

### **Momentive-AI (SurveyMonkey)** | Ottawa | *May 2020 – August 2021*
**Staff Software Engineer - Platform Architecture & Cross-Functional Leadership**

- **Led cross-functional initiative** for **multi-million GMV Brand Tracking platform**, directing **30+ person team** across Engineering, QA, and Product from concept to GA in **9 months**
- **Drove infrastructure modernization** delivering complete **rebrand initiative in 2 months** with zero downtime across all public-facing systems
- **Established security standards** through **Bug Bounty Program leadership**, strengthening data protection posture and organizational security practices

### **Veem** | Ottawa | *February 2019 – May 2020*
**Senior Software Engineer, Lead - Financial Platform Architecture & Team Leadership**

- **Led technical strategy** for **15+ person team** building B2B payment processing platform handling **$100M+ annual transaction volume** across global markets
- **Drove product innovation** launching **Veem Wallet contributing 4% of company revenue** in Q1 2020 with measurable business outcomes
- **Architected real-time payment integrations** with global financial institutions including **HSBC, SVB, Fidor** with regulatory compliance frameworks
- **Built enterprise integrations** with **Plaid, Trulioo, Coins.ph** for identity verification and international payment processing across multiple regulatory environments

### **Cisco Systems** | Ottawa | *February 2017 – February 2019*
**Senior Software Development Engineer - Global Platform Leadership**

- **Co-led global initiative** spanning **20+ engineers and 40 operations staff** across multiple geographic regions for network automation platform delivery
- **Served as technical lead** for XPRESSO dashboard and core contributor to **PyATS/Genie** open-source frameworks, influencing industry standards
- **Built distributed data collection systems** processing network telemetry from **10,000+ enterprise devices** with real-time monitoring and automated remediation

### **Earlier Experience** | *2008 – 2017*
**Nokia** - Software Development Engineer | **N-able** - Intermediate Software Developer

- **Nokia**: Developed core infrastructure modules for **5620 SAM** network management platform with enterprise-grade system reliability
- **N-able**: Owned critical platform modules in **N-central** and led security vulnerability remediation strengthening platform security posture

---

## Technical Leadership & Core Competencies

**Programming & Architecture:** Python, Java, Kotlin, JavaScript, Scala, Go, Spring Boot, Django, React
**Data & Infrastructure:** Apache Kafka, Spark, Snowflake, Elasticsearch, PostgreSQL, AWS (EC2, ECS, Lambda, S3, RDS)
**Integration & APIs:** REST APIs, GraphQL, OAuth 2.0, Microservices, Event-driven architecture, API Gateway
**Financial Services:** Payment processing, Regulatory compliance, Enterprise security, Real-time transaction systems

**Leadership Competencies:**
✓ **Cross-Organizational Leadership** - Led initiatives spanning 30+ engineers across multiple departments
✓ **Distributed Team Management** - Remote team coordination across time zones with seamless knowledge transfer
✓ **Technical Strategy** - Multi-year roadmaps for platforms processing billions of events and $100M+ volume
✓ **Talent Development** - Mentored 15+ senior engineers with sustainable knowledge transfer frameworks
✓ **Regulatory Expertise** - Deep experience in heavily regulated financial services environments
✓ **Performance Optimization** - Delivered 30-90% improvements and 99.9% uptime for business-critical systems

---

## Education & Recognition

**Bachelor of Science in Computer Science** | Carleton University, Ottawa

**Recognition:**
- **Speaker** - SurveyMonkey 2021 Annual Architecture Conference (Engineering Leadership)
- **Cisco Pioneer Award 2018** - DevX Productivity (PyATS/Genie open-source platform)
- **N-able Peer Awards** - Self-Reliance & Productivity (2009, 2010)
