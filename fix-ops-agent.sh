#!/bin/bash

# Fix Google Cloud Operations Agent Configuration
# This script deploys a working configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"
BACKUP_CONFIG="/etc/google-cloud-ops-agent/config.yaml.backup.$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}Fixing Google Cloud Operations Agent Configuration${NC}"
echo "=================================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root (use sudo)${NC}"
   exit 1
fi

# Backup existing config if it exists
if [ -f "$CONFIG_FILE" ]; then
    echo -e "${BLUE}Backing up existing configuration...${NC}"
    cp "$CONFIG_FILE" "$BACKUP_CONFIG"
    echo -e "${GREEN}Backup created: $BACKUP_CONFIG${NC}"
fi

# Create the config directory if it doesn't exist
mkdir -p /etc/google-cloud-ops-agent

# Deploy the working configuration
echo -e "${BLUE}Deploying working configuration...${NC}"

cat > "$CONFIG_FILE" << 'EOF'
logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /var/log/bittensor/*.log
        - /opt/bittensor/logs/*.log
        - ./logs/*.log
        - /home/<USER>/bittensor/logs/*.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      # Simple regex that handles the ANSI color codes
      regex: 'ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<log_message>.*)'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
EOF

# Set proper permissions
chmod 644 "$CONFIG_FILE"

echo -e "${GREEN}Configuration deployed successfully${NC}"

# Validate the configuration
echo -e "${BLUE}Validating configuration...${NC}"
if /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" --dry-run 2>/dev/null; then
    echo -e "${GREEN}Configuration is valid${NC}"
else
    echo -e "${YELLOW}Running basic validation check...${NC}"
    /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" || true
fi

# Restart the service
echo -e "${BLUE}Restarting Google Cloud Operations Agent...${NC}"
systemctl restart google-cloud-ops-agent

# Wait a moment for the service to start
sleep 3

# Check service status
if systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${GREEN}✅ Service is running successfully!${NC}"
    systemctl status google-cloud-ops-agent --no-pager -l
else
    echo -e "${RED}❌ Service failed to start${NC}"
    echo -e "${YELLOW}Service status:${NC}"
    systemctl status google-cloud-ops-agent --no-pager -l
    echo -e "${YELLOW}Recent logs:${NC}"
    journalctl -u google-cloud-ops-agent --no-pager -n 20
    exit 1
fi

echo -e "\n${GREEN}✅ Operations Agent is now configured and running!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Create log directory: mkdir -p /var/log/bittensor"
echo "2. Copy your log files to: /var/log/bittensor/"
echo "3. Monitor with: journalctl -u google-cloud-ops-agent -f"
echo "4. Check Google Cloud Console for parsed logs"
