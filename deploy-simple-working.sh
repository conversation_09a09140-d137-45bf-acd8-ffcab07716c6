#!/bin/bash

# Deploy Simple Working Google Cloud Operations Agent Configuration
# This script deploys a basic configuration that works with Ops Agent limitations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"
LOG_FILE="/home/<USER>/.pm2/logs/validator-out.log"

echo -e "${BLUE}Deploying Simple Working Operations Agent Configuration${NC}"
echo "====================================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root (use sudo)${NC}"
   exit 1
fi

# 1. Fix file permissions
echo -e "${BLUE}1. Fixing log file permissions...${NC}"
if [ -f "$LOG_FILE" ]; then
    chmod 644 "$LOG_FILE"
    echo -e "${GREEN}✅ Fixed permissions for $LOG_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  Log file not found: $LOG_FILE${NC}"
    echo -e "${YELLOW}   Configuration will still be deployed${NC}"
fi

# 2. Backup existing config
if [ -f "$CONFIG_FILE" ]; then
    BACKUP_FILE="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$CONFIG_FILE" "$BACKUP_FILE"
    echo -e "${GREEN}✅ Backed up config to: $BACKUP_FILE${NC}"
fi

# 3. Deploy the working configuration
echo -e "${BLUE}2. Deploying simple working configuration...${NC}"

cat > "$CONFIG_FILE" << 'EOF'
logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      # Simple regex that captures lines with timestamp and level
      regex: '\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \|(?:\s*(?P<log_message>.*?))?(?:\x1b\[[\d;]*m)*\s*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
EOF

chmod 644 "$CONFIG_FILE"
echo -e "${GREEN}✅ Configuration deployed${NC}"

# 4. Test the configuration
echo -e "${BLUE}3. Testing configuration...${NC}"
if /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" --dry-run 2>/dev/null; then
    echo -e "${GREEN}✅ Configuration is valid${NC}"
else
    echo -e "${YELLOW}⚠️  Running validation check...${NC}"
    /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" 2>&1 || true
fi

# 5. Restart the service
echo -e "${BLUE}4. Restarting Operations Agent...${NC}"
systemctl restart google-cloud-ops-agent

# Wait for service to start
sleep 3

# 6. Check service status
if systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${GREEN}✅ Service is running successfully!${NC}"
else
    echo -e "${RED}❌ Service failed to start${NC}"
    echo -e "${YELLOW}Service status:${NC}"
    systemctl status google-cloud-ops-agent --no-pager
    echo -e "${YELLOW}Recent logs:${NC}"
    journalctl -u google-cloud-ops-agent --no-pager -n 10
    exit 1
fi

echo -e "\n${GREEN}✅ Simple working configuration deployed successfully!${NC}"

echo -e "\n${YELLOW}Important Notes:${NC}"
echo "- This configuration captures individual log lines (not multi-line)"
echo "- Each line with timestamp becomes a separate log entry"
echo "- Configuration lines (netuid, auto_update, etc.) will be ignored"
echo "- This is the most reliable approach with Google Cloud Ops Agent"

echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Monitor logs: sudo journalctl -u google-cloud-ops-agent -f"
echo "2. Check GCP Console: Cloud Logging > Logs Explorer"
echo "3. Filter by: resource.type=\"gce_instance\""
echo "4. Look for parsed log entries with timestamp and level fields"

echo -e "\n${BLUE}Expected behavior:${NC}"
echo "- Lines like: \\x1b[34m2024-06-10 10:46:54.588\\x1b[0m | \\x1b[1m INFO \\x1b[0m | message"
echo "- Will appear as structured logs with timestamp, level, and message fields"
echo "- Configuration dump lines will be ignored (which is actually good for log clarity)"
