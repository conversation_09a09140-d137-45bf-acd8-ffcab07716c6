logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      # Simple regex that handles the ANSI color codes
      regex: 'ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<log_message>.*)'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
