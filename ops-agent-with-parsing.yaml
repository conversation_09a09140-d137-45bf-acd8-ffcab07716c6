logging:
  receivers:
    my_app_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      
  processors:
    parse_bittensor:
      type: parse_regex
      field: message
      # Regex to extract timestamp, level, and message from ANSI-coded logs
      regex: '\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \|(?:\s*(?P<log_message>.*?))?(?:\x1b\[[\d;]*m)*\s*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      # Don't drop logs that don't match - send them through unparsed
      parse_from: message
      preserve_original_text: true
      
  service:
    pipelines:
      default_pipeline:
        receivers: [my_app_logs]
        processors: [parse_bittensor]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
