**Highlights and Qualifications**

* Staff-level software engineer with 17+ years of hands-on experience across backend systems, data platforms, and integrations.  
* Proven track record of leading large cross-functional teams and delivering scalable platforms in high-growth environments.  
* Deep expertise in performance optimization, distributed systems, and third-party integrations.  
* Strong product and business mindset with ability to align architecture to long-term goals.  
* Versatile technologist with background in fintech, legal tech, SaaS, and enterprise systems.

**Work Experience**  
**Juniper Square, Remote**  
**Staff Software Engineer**	   	 		    		  **November 2023 – Present** 

* **Led the architecture and evolution** of a unified integration and data platform, processing **millions of records daily** across both inbound ingestion and outbound data publishing.  
* Designed and deployed a **Kafka-based event pipeline**, enabling scalable, decoupled data flows for analytics, mobile, and third-party integrations.  
* Drove **end-to-end performance and scalability initiatives**, improving **P95/P99 web request SLAs by 30–90%** and ensuring platform stability under sustained high-volume load.  
* Extended the platform’s reach to power internal data products and external-facing features, enabling broader reuse and faster delivery across teams.  
* Operate as a **technical lead and senior IC**, setting architectural direction, mentoring engineers, and delivering production-grade systems with lasting impact.

**CS Disco, Remote**  
**Software Architect**	   	 		    		     	**August 2021 – May 2023** 

* Directed platform initiatives across a team of 25+, focusing on the revenue platform and enterprise data warehouse.  
* Architected and delivered the **Limited Global Admin** system, defining team scopes and releasing MVP in under two months.  
* Built integrations with Salesforce, Dialpad, and Seismic; implemented OAuth2.0 flows; and delivered downstream pipelines to Snowflake.


**Momentive-AI (SurveyMonkey), Ottawa**  
**Staff Software Engineer**   	 		    		  	**May 2020 – August 2021** 

* Led development of a new multi-million GMV **Brand tracking solution**, from concept to GA in 9 months.  
* Directed a team of 30+ across engineering, QA, and product to scale the market research platform.  
* Delivered a 2-month confidential company-wide rebrand initiative, self-onboarding all public-facing engineering systems.  
* Participated in the Bug Bounty Program to triage and resolve security vulnerabilities.

**Veem, Ottawa**  
**Senior Software Engineer, Lead**  	 		    		**February 2019 – May 2020** 

* Led a 15+ person team building the B2B payment and funds movement platform.  
* Launched the **Veem Wallet**, contributing 4% of company revenue in Q1 2020 with \<1% user base.  
* Managed integrations with global partners including HSBC, SVB, Fidor, Plaid, Trulioo, and Coins.ph.

**Cisco Systems, Ottawa**  
**Senior Software Development Engineer**		 	        **February 2017 \- February 2019**

* Co-led a global team of 20+ engineers and 40 operations staff in network automation platform delivery.  
* Served as lead for XPRESSO dashboard and core contributor to the **PyATS** and **Genie** open-source telemetry frameworks.

**Nokia, Ottawa**  
**Software Development Engineer**		 		    **November 2011 – February 2017**

* Built key modules for **5620 SAM**, from requirements to deployment, with a focus on performance and reliability.

**N-able, Ottawa**  
**Intermediate Software Developer**		 			**July 2008 – November 2011**

* Owned core modules in the **N-central** product: data migration, watchdog, report generation, and third-party integration.  
* Discovered and mitigated multiple critical vulnerabilities, including XSS and path traversal.

**Recognition**

* Speaker at SurveyMonkey 2021 Annual Architecture Conference  
* Cisco Pioneer Award 2018 – DevX Productivity (PyATS/Genie platform)  
* N-able Peer Awards: Self-Reliance & Productivity (2009, 2010\)

**Education**  
**Bachelor of Science in Computer Science**		     	            Carleton University, Ottawa, Ontario

**Technologies**

Python, Kotlin, Java,  Javascript, Postgres, Redis, Elasticsearch, Snowflake, Spring Boot, Django, Angular, React, GraphQL, Docker, Kafka, Datadog, Terraform, AWS IAM, EC2, ECS Cluster, SQS, S3, DynamoDB, Lambda, Kinesis, RDS, SNS, StepFunction, API Gateway, SSM, Consul, Looker, Snowflake, OAuth, Microservice, CI/CD