#!/usr/bin/env python3
"""
Test script for multi-line log parsing
"""

import re
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

class MultilineBittensorLogParser:
    def __init__(self):
        # Pattern to identify the start of a new log entry
        self.log_start_pattern = re.compile(
            r'\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| '
            r'\x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \|'
        )
        
        # ANSI escape sequence cleaner
        self.ansi_cleaner = re.compile(r'\x1b\[[\d;]*m')
    
    def clean_ansi(self, text: str) -> str:
        """Remove ANSI escape sequences from text"""
        return self.ansi_cleaner.sub('', text)
    
    def parse_multiline_logs(self, filename: str) -> List[Dict]:
        """Parse log file handling multi-line entries"""
        results = []
        current_entry = None
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.rstrip('\n\r')
                    
                    # Check if this line starts a new log entry
                    match = self.log_start_pattern.match(line)
                    
                    if match:
                        # Save previous entry if exists
                        if current_entry:
                            results.append(current_entry)
                        
                        # Start new entry
                        timestamp_str = match.group('timestamp')
                        level = match.group('level').strip()
                        
                        # Extract message part after the last |
                        message_start = line.find('|', match.end())
                        if message_start != -1:
                            message = line[message_start + 1:].strip()
                        else:
                            message = ""
                        
                        # Clean ANSI codes from message
                        message = self.clean_ansi(message)
                        
                        # Parse timestamp
                        try:
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                        except ValueError:
                            timestamp = None
                        
                        current_entry = {
                            'timestamp': timestamp_str,
                            'parsed_timestamp': timestamp.isoformat() if timestamp else None,
                            'level': level,
                            'severity': self.map_severity(level),
                            'message': message,
                            'line_number': line_num,
                            'labels': {
                                'application': 'bittensor',
                                'component': 'validator'
                            }
                        }
                    else:
                        # This is a continuation line
                        if current_entry and line.strip():
                            # Clean the line and add to current message
                            clean_line = self.clean_ansi(line.strip())
                            if current_entry['message']:
                                current_entry['message'] += '\n' + clean_line
                            else:
                                current_entry['message'] = clean_line
                
                # Don't forget the last entry
                if current_entry:
                    results.append(current_entry)
                    
        except FileNotFoundError:
            print(f"File {filename} not found")
        except Exception as e:
            print(f"Error reading file {filename}: {e}")
        
        return results
    
    def map_severity(self, level: str) -> str:
        """Map log level to Google Cloud severity"""
        level_mapping = {
            'DEBUG': 'DEBUG',
            'INFO': 'INFO',
            'WARN': 'WARNING',
            'WARNING': 'WARNING',
            'ERROR': 'ERROR',
            'FATAL': 'CRITICAL',
            'CRITICAL': 'CRITICAL'
        }
        return level_mapping.get(level.upper(), 'INFO')

def main():
    parser = MultilineBittensorLogParser()
    
    # Test with actual PM2 log file
    pm2_log_file = '/home/<USER>/.pm2/logs/validator-out.log'
    
    print(f"Testing multi-line log parser with: {pm2_log_file}")
    
    if os.path.exists(pm2_log_file):
        results = parser.parse_multiline_logs(pm2_log_file)
        
        print(f"\nParsed {len(results)} multi-line log entries:")
        print("=" * 80)
        
        for i, entry in enumerate(results[:5], 1):  # Show first 5 entries
            print(f"\nEntry {i}:")
            print(f"  Timestamp: {entry['timestamp']}")
            print(f"  Level: {entry['level']}")
            print(f"  Severity: {entry['severity']}")
            print(f"  Message preview: {entry['message'][:200]}{'...' if len(entry['message']) > 200 else ''}")
            if '\n' in entry['message']:
                line_count = entry['message'].count('\n') + 1
                print(f"  Multi-line: {line_count} lines")
        
        # Export to JSON
        output_file = 'parsed_multiline_logs.json'
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nParsed logs exported to {output_file}")
        
        # Show statistics
        total_lines = sum(entry['message'].count('\n') + 1 for entry in results)
        multiline_entries = sum(1 for entry in results if '\n' in entry['message'])
        
        print(f"\nStatistics:")
        print(f"  Total log entries: {len(results)}")
        print(f"  Multi-line entries: {multiline_entries}")
        print(f"  Single-line entries: {len(results) - multiline_entries}")
        print(f"  Total message lines: {total_lines}")
        
    else:
        print(f"PM2 log file not found: {pm2_log_file}")
        print("Make sure the path is correct and PM2 is running")

if __name__ == "__main__":
    main()
