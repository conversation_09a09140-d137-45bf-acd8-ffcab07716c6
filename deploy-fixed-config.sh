#!/bin/bash

# Deploy Fixed Google Cloud Operations Agent Configuration
# This script deploys a configuration that handles real ANSI escape sequences

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"
LOG_FILE="/home/<USER>/.pm2/logs/validator-out.log"

echo -e "${BLUE}Deploying Fixed Operations Agent Configuration${NC}"
echo "=============================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root (use sudo)${NC}"
   exit 1
fi

# 1. Fix file permissions first
echo -e "${BLUE}1. Fixing log file permissions...${NC}"
if [ -f "$LOG_FILE" ]; then
    chmod 644 "$LOG_FILE"
    chown root:adm "$LOG_FILE" 2>/dev/null || true
    echo -e "${GREEN}✅ Fixed permissions for $LOG_FILE${NC}"
else
    echo -e "${RED}❌ Log file not found: $LOG_FILE${NC}"
    echo -e "${YELLOW}   Make sure PM2 is running and generating logs${NC}"
fi

# 2. Backup existing config
if [ -f "$CONFIG_FILE" ]; then
    BACKUP_FILE="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$CONFIG_FILE" "$BACKUP_FILE"
    echo -e "${GREEN}✅ Backed up config to: $BACKUP_FILE${NC}"
fi

# 3. Deploy the fixed configuration
echo -e "${BLUE}2. Deploying fixed configuration...${NC}"

cat > "$CONFIG_FILE" << 'EOF'
logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      # Regex that handles REAL ANSI escape sequences (\x1b instead of ESC)
      regex: '\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| (?P<log_message>.*)'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
EOF

chmod 644 "$CONFIG_FILE"
echo -e "${GREEN}✅ Configuration deployed${NC}"

# 4. Test the configuration
echo -e "${BLUE}3. Testing configuration...${NC}"
if /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" --dry-run 2>/dev/null; then
    echo -e "${GREEN}✅ Configuration is valid${NC}"
else
    echo -e "${RED}❌ Configuration validation failed${NC}"
    /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" 2>&1 || true
    exit 1
fi

# 5. Restart the service
echo -e "${BLUE}4. Restarting Operations Agent...${NC}"
systemctl restart google-cloud-ops-agent

# Wait for service to start
sleep 5

# 6. Check service status
if systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${GREEN}✅ Service is running${NC}"
else
    echo -e "${RED}❌ Service failed to start${NC}"
    systemctl status google-cloud-ops-agent --no-pager
    exit 1
fi

# 7. Show recent logs
echo -e "\n${BLUE}5. Recent Operations Agent logs:${NC}"
journalctl -u google-cloud-ops-agent --no-pager -n 10

echo -e "\n${GREEN}✅ Fixed configuration deployed successfully!${NC}"
echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Test parsing: python3 test_log_parser.py"
echo "2. Monitor logs: sudo journalctl -u google-cloud-ops-agent -f"
echo "3. Check GCP Console: Cloud Logging > Logs Explorer"
echo "4. Look for logs with resource.type=\"gce_instance\""

echo -e "\n${BLUE}If logs still don't appear in GCP Console:${NC}"
echo "- Verify GCP project: gcloud config get-value project"
echo "- Enable Logging API: gcloud services enable logging.googleapis.com"
echo "- Check VM service account has Logging Writer role"
