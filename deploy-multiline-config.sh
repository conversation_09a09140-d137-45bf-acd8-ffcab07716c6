#!/bin/bash

# Deploy Multi-line Google Cloud Operations Agent Configuration
# This script deploys a configuration that handles multi-line log entries

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"
LOG_FILE="/home/<USER>/.pm2/logs/validator-out.log"

echo -e "${BLUE}Deploying Multi-line Operations Agent Configuration${NC}"
echo "=================================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root (use sudo)${NC}"
   exit 1
fi

# 1. Fix file permissions
echo -e "${BLUE}1. Fixing log file permissions...${NC}"
if [ -f "$LOG_FILE" ]; then
    chmod 644 "$LOG_FILE"
    chown root:adm "$LOG_FILE" 2>/dev/null || true
    echo -e "${GREEN}✅ Fixed permissions for $LOG_FILE${NC}"
else
    echo -e "${RED}❌ Log file not found: $LOG_FILE${NC}"
    echo -e "${YELLOW}   Make sure PM2 is running and generating logs${NC}"
fi

# 2. Backup existing config
if [ -f "$CONFIG_FILE" ]; then
    BACKUP_FILE="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$CONFIG_FILE" "$BACKUP_FILE"
    echo -e "${GREEN}✅ Backed up config to: $BACKUP_FILE${NC}"
fi

# 3. Deploy the multi-line configuration
echo -e "${BLUE}2. Deploying multi-line configuration...${NC}"

cat > "$CONFIG_FILE" << 'EOF'
logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      # Multi-line configuration
      multiline:
        # Pattern that matches the start of a new log entry
        pattern: '\x1b\[[\d;]*m\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\x1b\[[\d;]*m \|'
        negate: false
        match: after
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      # Regex that handles both full lines and header-only lines
      # Now with multiline support
      regex: '\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| ?(?P<log_message>.*?)(?:\x1b\[[\d;]*m)*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
EOF

chmod 644 "$CONFIG_FILE"
echo -e "${GREEN}✅ Multi-line configuration deployed${NC}"

# 4. Test the configuration
echo -e "${BLUE}3. Testing configuration...${NC}"
if /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" --dry-run 2>/dev/null; then
    echo -e "${GREEN}✅ Configuration is valid${NC}"
else
    echo -e "${RED}❌ Configuration validation failed${NC}"
    /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" 2>&1 || true
    exit 1
fi

# 5. Restart the service
echo -e "${BLUE}4. Restarting Operations Agent...${NC}"
systemctl restart google-cloud-ops-agent

# Wait for service to start
sleep 5

# 6. Check service status
if systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${GREEN}✅ Service is running${NC}"
else
    echo -e "${RED}❌ Service failed to start${NC}"
    systemctl status google-cloud-ops-agent --no-pager
    exit 1
fi

# 7. Show recent logs
echo -e "\n${BLUE}5. Recent Operations Agent logs:${NC}"
journalctl -u google-cloud-ops-agent --no-pager -n 10

echo -e "\n${GREEN}✅ Multi-line configuration deployed successfully!${NC}"
echo -e "\n${YELLOW}Key Features:${NC}"
echo "- Handles multi-line log entries (config dumps, stack traces, etc.)"
echo "- Groups continuation lines with their parent log entry"
echo "- Preserves the complete message structure"

echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Test parsing: python3 test_multiline_parser.py"
echo "2. Monitor logs: sudo journalctl -u google-cloud-ops-agent -f"
echo "3. Check GCP Console: Cloud Logging > Logs Explorer"
echo "4. Look for complete multi-line messages in the log entries"

echo -e "\n${BLUE}Multi-line Log Behavior:${NC}"
echo "- Lines starting with timestamp+level = new log entry"
echo "- Lines without timestamp = continuation of previous entry"
echo "- All continuation lines are combined into single log message"
echo "- Preserves newlines and formatting in the message content"
