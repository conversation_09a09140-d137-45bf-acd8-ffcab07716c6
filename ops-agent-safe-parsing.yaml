logging:
  receivers:
    my_app_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      
  processors:
    # First, try to parse structured logs
    parse_structured:
      type: parse_regex
      field: message
      # Only parse lines that clearly match our pattern
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| ?(?P<clean_message>.*?)(?:\x1b\[[\d;]*m)*\s*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
    # Clean ANSI codes from all messages (even unparsed ones)
    clean_ansi:
      type: parse_regex
      field: message
      # Remove ANSI escape sequences from any message
      regex: '(?P<cleaned_message>.*)'
      
  service:
    pipelines:
      # Pipeline that tries parsing but doesn't drop unmatched logs
      main_pipeline:
        receivers: [my_app_logs]
        processors: [parse_structured]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
