ESC[34m2024-06-10 10:46:54.593ESC[0m | ESC[1m      INFO      ESC[0m | Setting up bittensor objects.
ESC[34m2024-06-10 10:46:54.594ESC[0m | ESC[1m      INFO      ESC[0m | Wallet: wallet(validator, default, ~/.bittensor/wallets/)
ESC[34m2024-06-10 10:46:57.998ESC[0m | ESC[1m      INFO      ESC[0m | Connected to test network and wss://test.finney.opentensor.ai:443/.
ESC[34m2024-06-10 10:46:57.998ESC[0m | ESC[1m      INFO      ESC[0m | Subtensor: subtensor(test, wss://test.finney.opentensor.ai:443/)
ESC[34m2024-06-10 10:46:58.237ESC[0m | ESC[1m      INFO      ESC[0m | Dendrite: dendrite(5Do3fsApuMwpPoscejsTGgtcHTzHdQDobUthghaWc2NzDKJo)
ESC[34m2024-06-10 10:47:04.108ESC[0m | ESC[1m      INFO      ESC[0m | Metagraph: metagraph(netuid:15, n:207, block:2101175, network:test)
ESC[34m2024-06-10 10:47:04.111ESC[0m | ESC[1m      INFO      ESC[0m | Building validation weights.
ESC[34m2024-06-10 10:47:04.115ESC[0m | ESC[1m      INFO      ESC[0m | 🔢 Initialized scores : [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
ESC[34m2024-06-10 10:47:10.869ESC[0m | ESC[1m      INFO      ESC[0m | ✅ Logging stats to Wandb.
ESC[31m2024-06-10 10:47:15.123ESC[0m | ESC[1m      ERROR     ESC[0m | Connection timeout to validator endpoint
ESC[33m2024-06-10 10:47:20.456ESC[0m | ESC[1m      WARN      ESC[0m | High memory usage detected: 85%
ESC[32m2024-06-10 10:47:25.789ESC[0m | ESC[1m      DEBUG     ESC[0m | Processing batch of 50 transactions
