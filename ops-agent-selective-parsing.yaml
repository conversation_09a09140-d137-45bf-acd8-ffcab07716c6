logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      
  processors:
    # Parse lines that have timestamp and level
    parse_timestamped_logs:
      type: parse_regex
      field: message
      # Comprehensive regex that handles:
      # 1. Full messages: \x1b[34m2024-06-10 10:46:54.588\x1b[0m | \x1b[1m INFO \x1b[0m | message content
      # 2. Header lines: \x1b[34m2024-06-10 10:46:54.593\x1b[0m | \x1b[1m INFO \x1b[0m |
      # 3. Multi-line content (everything after the |)
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \|(?P<message_content>.*?)(?:\x1b\[[\d;]*m)*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
    # Clean up the extracted message content
    clean_message:
      type: modify_fields
      fields:
        # Map severity levels to Google Cloud standards
        severity:
          copy_from: severity
          default_value: "INFO"
        # Clean the message content
        jsonPayload.original_message:
          copy_from: message_content
        jsonPayload.application:
          static_value: "bittensor"
        jsonPayload.component:
          static_value: "validator"
        
  service:
    pipelines:
      # Pipeline for timestamped log entries
      timestamped_logs:
        receivers: [bittensor_logs]
        processors: [parse_timestamped_logs, clean_message]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
