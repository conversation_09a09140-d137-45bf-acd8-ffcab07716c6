logging:
  receivers:
    bittensor_application_logs:
      type: files
      include_paths:
        - /var/log/bittensor/*.log
        - /opt/bittensor/logs/*.log
        - ./logs/*.log
        - /home/<USER>/bittensor/logs/*.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s
      
  processors:
    # First, strip ANSI color codes
    ansi_stripper:
      type: parse_regex
      field: message
      regex: '^(?P<clean_line>.*?)$'
      # This will be processed by the next processor
      
    # Parse the cleaned log line
    bittensor_structured_parser:
      type: parse_regex
      field: message
      # Updated regex to handle ANSI escape sequences
      regex: '\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| (?P<message_content>.*?)(?:\x1b\[[\d;]*m)*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
    # Alternative parser for already cleaned logs
    bittensor_simple_parser:
      type: parse_regex
      field: message
      regex: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) \| \s*(?P<level>\w+)\s* \| (?P<message_content>.*)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
    # Clean up message content
    message_cleaner:
      type: modify_fields
      fields:
        message:
          move_from: message_content
        log_level:
          move_from: level
        severity:
          copy_from: log_level
          default_value: "INFO"
          
    # Add custom labels
    label_enricher:
      type: modify_fields
      fields:
        labels.application:
          static_value: "bittensor"
        labels.component:
          static_value: "validator"
        labels.environment:
          static_value: "production"

  service:
    pipelines:
      bittensor_pipeline:
        receivers: [bittensor_application_logs]
        processors: [bittensor_structured_parser, message_cleaner, label_enricher]
      
      # Fallback pipeline for simpler parsing
      bittensor_simple_pipeline:
        receivers: [bittensor_application_logs]
        processors: [bittensor_simple_parser, message_cleaner, label_enricher]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  processors:
    metrics_filter:
      type: exclude_metrics
      metrics_pattern:
        - system.cpu.utilization
        - system.memory.utilization
        
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]
        processors: [metrics_filter]

# Global configuration
service:
  log_level: info
