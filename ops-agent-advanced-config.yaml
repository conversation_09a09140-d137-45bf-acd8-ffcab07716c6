logging:
  receivers:
    bittensor_application_logs:
      type: files
      include_paths:
        - /var/log/bittensor/*.log
        - /opt/bittensor/logs/*.log
        - ./logs/*.log
        - /home/<USER>/bittensor/logs/*.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse the log line with ANSI escape sequences
    bittensor_parser:
      type: parse_regex
      field: message
      # Regex to handle ANSI escape sequences - using ESC instead of \x1b for compatibility
      regex: 'ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<log_message>.*?)(?:ESC\[[\d;]*m)*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"

    # Add severity mapping and labels
    field_enricher:
      type: modify_fields
      fields:
        severity:
          copy_from: level
          default_value: "INFO"
        labels.application:
          static_value: "bittensor"
        labels.component:
          static_value: "validator"
        labels.environment:
          static_value: "production"

  service:
    pipelines:
      bittensor_pipeline:
        receivers: [bittensor_application_logs]
        processors: [bittensor_parser, field_enricher]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  processors:
    metrics_filter:
      type: exclude_metrics
      metrics_pattern:
        - system.cpu.utilization
        - system.memory.utilization
        
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]
        processors: [metrics_filter]

# Global configuration
service:
  log_level: info
