logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      
  processors:
    parse_bittensor:
      type: parse_regex
      field: message
      # Extract timestamp, severity, and message from ANSI-coded logs
      # The named groups automatically become fields in the log entry
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \|(?P<parsed_message>.*?)(?:\x1b\[[\d;]*m)*\s*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
  service:
    pipelines:
      default_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_bittensor]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

service:
  log_level: info
