#!/usr/bin/env python3
"""
Test script to validate log parsing for Google Cloud Operations Agent
"""

import re
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

class BittensorLogParser:
    def __init__(self):
        # Regex pattern to match the log format with REAL ANSI color codes (\x1b)
        self.ansi_pattern = re.compile(
            r'\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| '
            r'\x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| (?P<message>.*?)(?:\x1b\[[\d;]*m)*$'
        )

        # Alternative pattern for logs without ANSI codes
        self.clean_pattern = re.compile(
            r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) \| '
            r'\s*(?P<level>\w+)\s* \| (?P<message>.*)$'
        )

        # ANSI escape sequence cleaner (handles real \x1b sequences)
        self.ansi_cleaner = re.compile(r'\x1b\[[\d;]*m')

        # Also handle literal "ESC" text (for sample files)
        self.literal_esc_pattern = re.compile(
            r'ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| '
            r'ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<message>.*?)(?:ESC\[[\d;]*m)*$'
        )
        self.literal_esc_cleaner = re.compile(r'ESC\[[\d;]*m')
    
    def clean_ansi(self, text: str) -> str:
        """Remove ANSI escape sequences from text"""
        # Clean both real ANSI codes and literal "ESC" text
        text = self.ansi_cleaner.sub('', text)
        text = self.literal_esc_cleaner.sub('', text)
        return text
    
    def parse_log_line(self, line: str) -> Optional[Dict]:
        """Parse a single log line and extract structured data"""
        line = line.strip()
        if not line:
            return None
        
        # Try ANSI pattern first
        match = self.ansi_pattern.match(line)
        if not match:
            # Try clean pattern
            clean_line = self.clean_ansi(line)
            match = self.clean_pattern.match(clean_line)
        
        if match:
            timestamp_str = match.group('timestamp')
            level = match.group('level').strip()
            message = match.group('message').strip()
            
            # Clean any remaining ANSI codes from message
            message = self.clean_ansi(message)
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
            except ValueError:
                timestamp = None
            
            return {
                'timestamp': timestamp_str,
                'parsed_timestamp': timestamp.isoformat() if timestamp else None,
                'level': level,
                'severity': self.map_severity(level),
                'message': message,
                'labels': {
                    'application': 'bittensor',
                    'component': 'validator'
                }
            }
        
        return None
    
    def map_severity(self, level: str) -> str:
        """Map log level to Google Cloud severity"""
        level_mapping = {
            'DEBUG': 'DEBUG',
            'INFO': 'INFO',
            'WARN': 'WARNING',
            'WARNING': 'WARNING',
            'ERROR': 'ERROR',
            'FATAL': 'CRITICAL',
            'CRITICAL': 'CRITICAL'
        }
        return level_mapping.get(level.upper(), 'INFO')
    
    def parse_file(self, filename: str) -> List[Dict]:
        """Parse entire log file"""
        results = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    parsed = self.parse_log_line(line)
                    if parsed:
                        parsed['line_number'] = line_num
                        results.append(parsed)
                    else:
                        print(f"Failed to parse line {line_num}: {line.strip()}")
        except FileNotFoundError:
            print(f"File {filename} not found")
        except Exception as e:
            print(f"Error reading file {filename}: {e}")
        
        return results

def main():
    parser = BittensorLogParser()

    # Test with actual PM2 log file first
    pm2_log_file = '/home/<USER>/.pm2/logs/validator-out.log'

    print(f"Testing log parser with actual PM2 log file: {pm2_log_file}")
    if os.path.exists(pm2_log_file):
        print("Analyzing actual log format...")

        # Read first few lines to understand format
        try:
            with open(pm2_log_file, 'r') as f:
                sample_lines = [f.readline().strip() for _ in range(5)]
                sample_lines = [line for line in sample_lines if line]

            print(f"\nFound {len(sample_lines)} sample lines:")
            for i, line in enumerate(sample_lines, 1):
                print(f"Line {i}: {repr(line)}")

                # Test current regex
                parsed = parser.parse_log_line(line)
                if parsed:
                    print(f"  ✅ Parsed successfully")
                else:
                    print(f"  ❌ Failed to parse")

                    # Try to suggest a better regex
                    if '|' in line:
                        parts = line.split('|')
                        print(f"     Line has {len(parts)} parts: {[p.strip() for p in parts]}")

            # Parse the file
            results = parser.parse_file(pm2_log_file)
            print(f"\nSuccessfully parsed {len(results)} entries from PM2 log")

        except Exception as e:
            print(f"Error reading PM2 log file: {e}")
            results = []
    else:
        print(f"PM2 log file not found: {pm2_log_file}")
        print("Testing with sample log file instead...")
        results = parser.parse_file('sample-bittensor.log')
    
    print(f"\nParsed {len(results)} log entries:")
    print("=" * 80)
    
    for i, entry in enumerate(results, 1):
        print(f"\nEntry {i}:")
        print(f"  Timestamp: {entry['timestamp']}")
        print(f"  Level: {entry['level']}")
        print(f"  Severity: {entry['severity']}")
        print(f"  Message: {entry['message'][:100]}{'...' if len(entry['message']) > 100 else ''}")
    
    # Export to JSON for Google Cloud Operations Agent testing
    output_file = 'parsed_logs.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nParsed logs exported to {output_file}")
    
    # Test individual log line parsing
    print("\n" + "=" * 80)
    print("Testing individual log line parsing:")
    
    test_lines = [
        "ESC[34m2024-06-10 10:46:54.593ESC[0m | ESC[1m      INFO      ESC[0m | Setting up bittensor objects.",
        "ESC[31m2024-06-10 10:47:15.123ESC[0m | ESC[1m      ERROR     ESC[0m | Connection timeout to validator endpoint",
        "2024-06-10 10:47:20.456 |       WARN       | High memory usage detected: 85%"
    ]
    
    for line in test_lines:
        print(f"\nOriginal: {line}")
        parsed = parser.parse_log_line(line)
        if parsed:
            print(f"Parsed: {json.dumps(parsed, indent=2, default=str)}")
        else:
            print("Failed to parse")

if __name__ == "__main__":
    main()
