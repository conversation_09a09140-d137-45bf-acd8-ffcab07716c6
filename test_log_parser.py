#!/usr/bin/env python3
"""
Test script to validate log parsing for Google Cloud Operations Agent
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Optional

class BittensorLogParser:
    def __init__(self):
        # Regex pattern to match the log format with ANSI color codes
        self.ansi_pattern = re.compile(
            r'ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| '
            r'ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<message>.*?)(?:ESC\[[\d;]*m)*$'
        )
        
        # Alternative pattern for logs without ANSI codes
        self.clean_pattern = re.compile(
            r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) \| '
            r'\s*(?P<level>\w+)\s* \| (?P<message>.*)$'
        )
        
        # ANSI escape sequence cleaner
        self.ansi_cleaner = re.compile(r'ESC\[[\d;]*m')
    
    def clean_ansi(self, text: str) -> str:
        """Remove ANSI escape sequences from text"""
        return self.ansi_cleaner.sub('', text)
    
    def parse_log_line(self, line: str) -> Optional[Dict]:
        """Parse a single log line and extract structured data"""
        line = line.strip()
        if not line:
            return None
        
        # Try ANSI pattern first
        match = self.ansi_pattern.match(line)
        if not match:
            # Try clean pattern
            clean_line = self.clean_ansi(line)
            match = self.clean_pattern.match(clean_line)
        
        if match:
            timestamp_str = match.group('timestamp')
            level = match.group('level').strip()
            message = match.group('message').strip()
            
            # Clean any remaining ANSI codes from message
            message = self.clean_ansi(message)
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
            except ValueError:
                timestamp = None
            
            return {
                'timestamp': timestamp_str,
                'parsed_timestamp': timestamp.isoformat() if timestamp else None,
                'level': level,
                'severity': self.map_severity(level),
                'message': message,
                'labels': {
                    'application': 'bittensor',
                    'component': 'validator'
                }
            }
        
        return None
    
    def map_severity(self, level: str) -> str:
        """Map log level to Google Cloud severity"""
        level_mapping = {
            'DEBUG': 'DEBUG',
            'INFO': 'INFO',
            'WARN': 'WARNING',
            'WARNING': 'WARNING',
            'ERROR': 'ERROR',
            'FATAL': 'CRITICAL',
            'CRITICAL': 'CRITICAL'
        }
        return level_mapping.get(level.upper(), 'INFO')
    
    def parse_file(self, filename: str) -> List[Dict]:
        """Parse entire log file"""
        results = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    parsed = self.parse_log_line(line)
                    if parsed:
                        parsed['line_number'] = line_num
                        results.append(parsed)
                    else:
                        print(f"Failed to parse line {line_num}: {line.strip()}")
        except FileNotFoundError:
            print(f"File {filename} not found")
        except Exception as e:
            print(f"Error reading file {filename}: {e}")
        
        return results

def main():
    parser = BittensorLogParser()
    
    # Test with sample log file
    print("Testing log parser with sample-bittensor.log...")
    results = parser.parse_file('sample-bittensor.log')
    
    print(f"\nParsed {len(results)} log entries:")
    print("=" * 80)
    
    for i, entry in enumerate(results, 1):
        print(f"\nEntry {i}:")
        print(f"  Timestamp: {entry['timestamp']}")
        print(f"  Level: {entry['level']}")
        print(f"  Severity: {entry['severity']}")
        print(f"  Message: {entry['message'][:100]}{'...' if len(entry['message']) > 100 else ''}")
    
    # Export to JSON for Google Cloud Operations Agent testing
    output_file = 'parsed_logs.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nParsed logs exported to {output_file}")
    
    # Test individual log line parsing
    print("\n" + "=" * 80)
    print("Testing individual log line parsing:")
    
    test_lines = [
        "ESC[34m2024-06-10 10:46:54.593ESC[0m | ESC[1m      INFO      ESC[0m | Setting up bittensor objects.",
        "ESC[31m2024-06-10 10:47:15.123ESC[0m | ESC[1m      ERROR     ESC[0m | Connection timeout to validator endpoint",
        "2024-06-10 10:47:20.456 |       WARN       | High memory usage detected: 85%"
    ]
    
    for line in test_lines:
        print(f"\nOriginal: {line}")
        parsed = parser.parse_log_line(line)
        if parsed:
            print(f"Parsed: {json.dumps(parsed, indent=2, default=str)}")
        else:
            print("Failed to parse")

if __name__ == "__main__":
    main()
