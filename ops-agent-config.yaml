logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /var/log/bittensor/*.log
        - /opt/bittensor/logs/*.log
        - ./logs/*.log
      exclude_paths:
        - /var/log/bittensor/*.gz
      record_log_file_path: true
      wildcard_refresh_interval: 60s
      
  processors:
    bittensor_parser:
      type: parse_regex
      field: message
      regex: '^ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<log_message>.*)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
      
    ansi_cleaner:
      type: parse_regex
      field: log_message
      regex: '^(?P<clean_message>.*?)(?:ESC\[[\d;]*m)*$'
      
    severity_mapper:
      type: modify_fields
      fields:
        severity:
          copy_from: level
          default_value: "INFO"
          
  service:
    pipelines:
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [bittensor_parser, ansi_cleaner, severity_mapper]

metrics:
  receivers:
    hostmetrics:
      type: hostmetrics
      collection_interval: 60s
      
  service:
    pipelines:
      default_pipeline:
        receivers: [hostmetrics]

# Global service configuration
service:
  log_level: info
  pipelines:
    bittensor_logs:
      receivers: [bittensor_logs]
      processors: [bittensor_parser, ansi_cleaner, severity_mapper]
