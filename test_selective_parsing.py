#!/usr/bin/env python3
"""
Test selective parsing configuration before deploying to Operations Agent
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Optional

def test_selective_parsing():
    # The regex pattern from our configuration
    pattern = r'^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \|(?P<message_content>.*?)(?:\x1b\[[\d;]*m)*$'
    
    log_file = "/home/<USER>/.pm2/logs/validator-out.log"
    
    print("Testing Selective Parsing Configuration")
    print("=" * 50)
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        matched_lines = 0
        parsed_entries = []
        
        print(f"Total lines in log file: {total_lines}")
        print("\nTesting each line:")
        print("-" * 30)
        
        for i, line in enumerate(lines[:20], 1):  # Test first 20 lines
            line = line.strip()
            if not line:
                continue
                
            match = re.match(pattern, line)
            
            if match:
                matched_lines += 1
                timestamp = match.group('timestamp')
                severity = match.group('severity')
                message_content = match.group('message_content')
                
                # Clean ANSI codes from message
                clean_message = re.sub(r'\x1b\[[\d;]*m', '', message_content).strip()
                
                parsed_entry = {
                    'line_number': i,
                    'timestamp': timestamp,
                    'severity': severity,
                    'message': clean_message,
                    'original_line_preview': line[:100] + '...' if len(line) > 100 else line
                }
                
                parsed_entries.append(parsed_entry)
                
                print(f"✅ Line {i}: MATCHED")
                print(f"   Timestamp: {timestamp}")
                print(f"   Severity: {severity}")
                print(f"   Message: {clean_message[:60]}{'...' if len(clean_message) > 60 else ''}")
                
            else:
                print(f"❌ Line {i}: NO MATCH")
                print(f"   Content: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        # Statistics
        print(f"\n" + "=" * 50)
        print("PARSING STATISTICS")
        print("=" * 50)
        print(f"Total lines tested: {min(20, total_lines)}")
        print(f"Successfully parsed: {matched_lines}")
        print(f"Would be dropped: {min(20, total_lines) - matched_lines}")
        print(f"Success rate: {(matched_lines / min(20, total_lines)) * 100:.1f}%")
        
        if total_lines > 20:
            print(f"\nNote: Only tested first 20 lines of {total_lines} total lines")
        
        # Show sample parsed entries
        if parsed_entries:
            print(f"\n" + "=" * 50)
            print("SAMPLE PARSED ENTRIES")
            print("=" * 50)
            
            for entry in parsed_entries[:3]:
                print(f"\nEntry {entry['line_number']}:")
                print(f"  Timestamp: {entry['timestamp']}")
                print(f"  Severity: {entry['severity']}")
                print(f"  Message: {entry['message']}")
        
        # Severity mapping
        severities = [entry['severity'] for entry in parsed_entries]
        unique_severities = set(severities)
        
        if unique_severities:
            print(f"\n" + "=" * 50)
            print("SEVERITY LEVELS FOUND")
            print("=" * 50)
            for severity in sorted(unique_severities):
                count = severities.count(severity)
                print(f"  {severity}: {count} entries")
        
        # Multi-line detection
        multiline_entries = [entry for entry in parsed_entries if '\n' in entry['message'] or len(entry['message']) > 200]
        
        if multiline_entries:
            print(f"\n" + "=" * 50)
            print("MULTI-LINE MESSAGES DETECTED")
            print("=" * 50)
            print(f"Found {len(multiline_entries)} potentially multi-line entries")
        
        return matched_lines > 0
        
    except FileNotFoundError:
        print(f"❌ Log file not found: {log_file}")
        print("Make sure PM2 is running and the path is correct")
        return False
    except Exception as e:
        print(f"❌ Error testing parsing: {e}")
        return False

def main():
    success = test_selective_parsing()
    
    print(f"\n" + "=" * 50)
    print("RECOMMENDATION")
    print("=" * 50)
    
    if success:
        print("✅ Parsing looks good! You can deploy the selective parsing configuration.")
        print("\nTo deploy:")
        print("  sudo cp ops-agent-selective-parsing.yaml /etc/google-cloud-ops-agent/config.yaml")
        print("  sudo systemctl restart google-cloud-ops-agent")
        print("\nExpected results:")
        print("  - Logs with timestamps will be parsed with proper timestamp and severity")
        print("  - Configuration dump lines will be dropped (which is actually cleaner)")
        print("  - Multi-line messages will be captured in the message field")
    else:
        print("❌ No lines matched the parsing pattern.")
        print("\nThis means:")
        print("  - All logs would be dropped with selective parsing")
        print("  - Stick with the simple configuration for now")
        print("  - Check if the log format has changed")

if __name__ == "__main__":
    main()
