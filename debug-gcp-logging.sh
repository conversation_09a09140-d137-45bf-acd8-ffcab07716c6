#!/bin/bash

# Comprehensive GCP Logging Debug Script
# This script checks all aspects of Google Cloud Operations Agent logging

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="/home/<USER>/.pm2/logs/validator-out.log"
CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"

echo -e "${BLUE}Google Cloud Operations Agent Comprehensive Debug${NC}"
echo "================================================="

# 1. Check GCP metadata and project
echo -e "\n${BLUE}1. Checking GCP VM metadata...${NC}"
if curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/project/project-id 2>/dev/null; then
    PROJECT_ID=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/project/project-id)
    echo -e "${GREEN}✅ Running on GCP VM${NC}"
    echo -e "   Project ID: $PROJECT_ID"
    
    ZONE=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/zone | cut -d/ -f4)
    INSTANCE_NAME=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/name)
    echo -e "   Zone: $ZONE"
    echo -e "   Instance: $INSTANCE_NAME"
else
    echo -e "${RED}❌ Not running on GCP VM or metadata service unavailable${NC}"
fi

# 2. Check service account and permissions
echo -e "\n${BLUE}2. Checking service account...${NC}"
if command -v gcloud &> /dev/null; then
    ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>/dev/null || echo "none")
    echo -e "   Active account: $ACCOUNT"
    
    if [ "$ACCOUNT" != "none" ]; then
        echo -e "${GREEN}✅ gcloud is authenticated${NC}"
        
        # Test if we can write to Cloud Logging
        echo -e "   Testing Cloud Logging API access..."
        if gcloud logging write test-log "Test message from debug script" --severity=INFO 2>/dev/null; then
            echo -e "${GREEN}✅ Can write to Cloud Logging${NC}"
        else
            echo -e "${RED}❌ Cannot write to Cloud Logging${NC}"
            echo -e "${YELLOW}   Check IAM permissions: Logging Writer role${NC}"
        fi
    else
        echo -e "${RED}❌ gcloud not authenticated${NC}"
        echo -e "${YELLOW}   Run: gcloud auth application-default login${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  gcloud CLI not installed${NC}"
fi

# Check default service account
DEFAULT_SA=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/email 2>/dev/null || echo "none")
if [ "$DEFAULT_SA" != "none" ]; then
    echo -e "   Default service account: $DEFAULT_SA"
    
    # Check scopes
    SCOPES=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/scopes 2>/dev/null || echo "none")
    if echo "$SCOPES" | grep -q "logging.write\|cloud-platform"; then
        echo -e "${GREEN}✅ Service account has logging scopes${NC}"
    else
        echo -e "${RED}❌ Service account missing logging scopes${NC}"
        echo -e "${YELLOW}   Required scope: https://www.googleapis.com/auth/logging.write${NC}"
    fi
fi

# 3. Check Operations Agent service
echo -e "\n${BLUE}3. Checking Operations Agent service...${NC}"
if systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${GREEN}✅ Service is running${NC}"
    
    # Check how long it's been running
    UPTIME=$(systemctl show google-cloud-ops-agent --property=ActiveEnterTimestamp --value)
    echo -e "   Started: $UPTIME"
    
    # Check if it's actually processing logs
    echo -e "   Recent service logs:"
    journalctl -u google-cloud-ops-agent --no-pager -n 5 --since "5 minutes ago" | sed 's/^/     /'
else
    echo -e "${RED}❌ Service is not running${NC}"
    systemctl status google-cloud-ops-agent --no-pager
fi

# 4. Check log file
echo -e "\n${BLUE}4. Checking log file...${NC}"
if [ -f "$LOG_FILE" ]; then
    echo -e "${GREEN}✅ Log file exists: $LOG_FILE${NC}"
    
    FILE_SIZE=$(stat -c%s "$LOG_FILE" 2>/dev/null || stat -f%z "$LOG_FILE" 2>/dev/null)
    echo -e "   Size: $FILE_SIZE bytes"
    
    PERMISSIONS=$(ls -la "$LOG_FILE")
    echo -e "   Permissions: $PERMISSIONS"
    
    # Check if ops agent can read it
    if sudo -u google-cloud-ops-agent test -r "$LOG_FILE" 2>/dev/null; then
        echo -e "${GREEN}✅ Ops agent can read the file${NC}"
    else
        echo -e "${RED}❌ Ops agent cannot read the file${NC}"
        echo -e "${YELLOW}   Fix: sudo chmod 644 $LOG_FILE${NC}"
    fi
    
    # Show recent content
    echo -e "   Last 3 lines:"
    tail -n 3 "$LOG_FILE" | sed 's/^/     /' | cat -A
    
    # Check if file is being written to
    LAST_MODIFIED=$(stat -c %Y "$LOG_FILE" 2>/dev/null || stat -f %m "$LOG_FILE" 2>/dev/null)
    CURRENT_TIME=$(date +%s)
    AGE=$((CURRENT_TIME - LAST_MODIFIED))
    
    if [ $AGE -lt 300 ]; then
        echo -e "${GREEN}✅ File was modified recently (${AGE}s ago)${NC}"
    else
        echo -e "${YELLOW}⚠️  File was last modified ${AGE}s ago${NC}"
        echo -e "${YELLOW}   Check if PM2 is still writing logs${NC}"
    fi
else
    echo -e "${RED}❌ Log file does not exist: $LOG_FILE${NC}"
    echo -e "${YELLOW}   Check PM2 status: pm2 status${NC}"
fi

# 5. Check configuration
echo -e "\n${BLUE}5. Checking configuration...${NC}"
if [ -f "$CONFIG_FILE" ]; then
    echo -e "${GREEN}✅ Configuration file exists${NC}"
    
    # Validate syntax
    if /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" --dry-run 2>/dev/null; then
        echo -e "${GREEN}✅ Configuration syntax is valid${NC}"
    else
        echo -e "${RED}❌ Configuration has errors:${NC}"
        /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" 2>&1 | sed 's/^/     /'
    fi
    
    # Show current config
    echo -e "   Current configuration:"
    cat "$CONFIG_FILE" | sed 's/^/     /'
else
    echo -e "${RED}❌ Configuration file missing: $CONFIG_FILE${NC}"
fi

# 6. Test regex pattern locally
echo -e "\n${BLUE}6. Testing regex pattern...${NC}"
if [ -f "$LOG_FILE" ] && command -v python3 &> /dev/null; then
    python3 - << 'EOF'
import re

log_file = "/home/<USER>/.pm2/logs/validator-out.log"
pattern = r'\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \|(?:\s*(?P<log_message>.*?))?(?:\x1b\[[\d;]*m)*\s*$'

try:
    with open(log_file, 'r') as f:
        lines = [f.readline().strip() for _ in range(5)]
        lines = [line for line in lines if line]
    
    matches = 0
    for i, line in enumerate(lines, 1):
        match = re.search(pattern, line)
        if match:
            matches += 1
            print(f"✅ Line {i}: MATCHED")
            print(f"   Timestamp: {match.group('timestamp')}")
            print(f"   Level: {match.group('level')}")
            message = match.group('log_message') or ""
            print(f"   Message: {message[:50]}{'...' if len(message) > 50 else ''}")
        else:
            print(f"❌ Line {i}: NO MATCH")
            print(f"   Content: {repr(line[:100])}")
    
    print(f"\nSummary: {matches}/{len(lines)} lines matched the regex pattern")
    
except Exception as e:
    print(f"Error testing regex: {e}")
EOF
else
    echo -e "${YELLOW}⚠️  Cannot test regex (missing file or python3)${NC}"
fi

# 7. Check network connectivity
echo -e "\n${BLUE}7. Checking network connectivity...${NC}"
if curl -s --max-time 5 https://logging.googleapis.com > /dev/null; then
    echo -e "${GREEN}✅ Can reach Google Cloud Logging API${NC}"
else
    echo -e "${RED}❌ Cannot reach Google Cloud Logging API${NC}"
    echo -e "${YELLOW}   Check firewall rules and network connectivity${NC}"
fi

# 8. Manual log test
echo -e "\n${BLUE}8. Manual log test...${NC}"
if command -v gcloud &> /dev/null; then
    echo -e "   Sending test log entry..."
    if gcloud logging write ops-agent-test "Manual test from debug script - $(date)" --severity=INFO 2>/dev/null; then
        echo -e "${GREEN}✅ Manual log entry sent successfully${NC}"
        echo -e "${YELLOW}   Check GCP Console > Logging > Logs Explorer for 'ops-agent-test'${NC}"
    else
        echo -e "${RED}❌ Failed to send manual log entry${NC}"
    fi
fi

# 9. Recommendations
echo -e "\n${BLUE}9. Recommendations:${NC}"
echo "=================================="

if ! systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${RED}• Fix Operations Agent service first${NC}"
    echo "  sudo systemctl restart google-cloud-ops-agent"
fi

if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}• Fix log file path or start PM2${NC}"
    echo "  pm2 start your-app"
    echo "  pm2 logs"
fi

if ! curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/project/project-id &>/dev/null; then
    echo -e "${RED}• Ensure running on GCP VM with proper metadata${NC}"
fi

echo -e "${YELLOW}• Check GCP Console:${NC}"
echo "  1. Go to Cloud Logging > Logs Explorer"
echo "  2. Filter: resource.type=\"gce_instance\""
echo "  3. Filter: resource.labels.instance_id=\"$INSTANCE_NAME\""
echo "  4. Look for recent entries"

echo -e "${YELLOW}• Monitor in real-time:${NC}"
echo "  sudo journalctl -u google-cloud-ops-agent -f"

echo -e "\n${GREEN}Debug complete!${NC}"
