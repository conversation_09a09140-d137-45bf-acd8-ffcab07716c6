#!/bin/bash

# Debug Google Cloud Operations Agent for Bittensor Logs
# This script helps identify why logs aren't appearing in GCP console

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_FILE="/home/<USER>/.pm2/logs/validator-out.log"
CONFIG_FILE="/etc/google-cloud-ops-agent/config.yaml"

echo -e "${BLUE}Google Cloud Operations Agent Debug Tool${NC}"
echo "========================================"

# 1. Check if log file exists and permissions
echo -e "\n${BLUE}1. Checking log file...${NC}"
if [ -f "$LOG_FILE" ]; then
    echo -e "${GREEN}✅ Log file exists: $LOG_FILE${NC}"
    
    # Check file size
    FILE_SIZE=$(stat -c%s "$LOG_FILE" 2>/dev/null || stat -f%z "$LOG_FILE" 2>/dev/null || echo "unknown")
    echo -e "   File size: $FILE_SIZE bytes"
    
    # Check permissions
    echo -e "   Permissions: $(ls -la "$LOG_FILE")"
    
    # Check if ops agent user can read it
    if sudo -u google-cloud-ops-agent test -r "$LOG_FILE" 2>/dev/null; then
        echo -e "${GREEN}✅ Ops agent can read the file${NC}"
    else
        echo -e "${RED}❌ Ops agent cannot read the file${NC}"
        echo -e "${YELLOW}   Fix: sudo chmod 644 $LOG_FILE${NC}"
    fi
else
    echo -e "${RED}❌ Log file does not exist: $LOG_FILE${NC}"
    echo -e "${YELLOW}   Check if the path is correct and PM2 is running${NC}"
    exit 1
fi

# 2. Show sample log lines
echo -e "\n${BLUE}2. Sample log content (last 5 lines):${NC}"
echo "----------------------------------------"
tail -n 5 "$LOG_FILE" | cat -A
echo "----------------------------------------"

# 3. Show first few lines to understand format
echo -e "\n${BLUE}3. First few log lines:${NC}"
echo "----------------------------------------"
head -n 3 "$LOG_FILE" | cat -A
echo "----------------------------------------"

# 4. Check ops agent service status
echo -e "\n${BLUE}4. Operations Agent service status:${NC}"
if systemctl is-active --quiet google-cloud-ops-agent; then
    echo -e "${GREEN}✅ Service is running${NC}"
else
    echo -e "${RED}❌ Service is not running${NC}"
    systemctl status google-cloud-ops-agent --no-pager
fi

# 5. Check recent ops agent logs
echo -e "\n${BLUE}5. Recent Operations Agent logs:${NC}"
echo "----------------------------------------"
journalctl -u google-cloud-ops-agent --no-pager -n 10 --since "5 minutes ago"
echo "----------------------------------------"

# 6. Test configuration syntax
echo -e "\n${BLUE}6. Testing configuration syntax:${NC}"
if [ -f "$CONFIG_FILE" ]; then
    if /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" --dry-run 2>/dev/null; then
        echo -e "${GREEN}✅ Configuration syntax is valid${NC}"
    else
        echo -e "${RED}❌ Configuration has errors:${NC}"
        /opt/google-cloud-ops-agent/libexec/google_cloud_ops_agent_engine -in "$CONFIG_FILE" 2>&1 || true
    fi
else
    echo -e "${RED}❌ Configuration file not found: $CONFIG_FILE${NC}"
fi

# 7. Check GCP project and permissions
echo -e "\n${BLUE}7. Checking GCP project setup:${NC}"
if command -v gcloud &> /dev/null; then
    PROJECT=$(gcloud config get-value project 2>/dev/null || echo "not-set")
    echo -e "   Current project: $PROJECT"
    
    if [ "$PROJECT" != "not-set" ]; then
        echo -e "${GREEN}✅ GCP project is configured${NC}"
        
        # Check if logging API is enabled
        if gcloud services list --enabled --filter="name:logging.googleapis.com" --format="value(name)" 2>/dev/null | grep -q logging; then
            echo -e "${GREEN}✅ Cloud Logging API is enabled${NC}"
        else
            echo -e "${RED}❌ Cloud Logging API is not enabled${NC}"
            echo -e "${YELLOW}   Fix: gcloud services enable logging.googleapis.com${NC}"
        fi
    else
        echo -e "${RED}❌ No GCP project configured${NC}"
        echo -e "${YELLOW}   Fix: gcloud config set project YOUR_PROJECT_ID${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  gcloud CLI not found - cannot check GCP setup${NC}"
fi

# 8. Test regex pattern against actual log content
echo -e "\n${BLUE}8. Testing regex pattern:${NC}"
python3 - << 'EOF'
import re
import sys

# Read a few lines from the log file
log_file = "/home/<USER>/.pm2/logs/validator-out.log"
try:
    with open(log_file, 'r') as f:
        lines = [f.readline().strip() for _ in range(3)]
        lines = [line for line in lines if line]  # Remove empty lines
except Exception as e:
    print(f"Error reading log file: {e}")
    sys.exit(1)

# Test our current regex
current_regex = r'ESC\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})ESC\[[\d;]*m \| ESC\[[\d;]*m\s*(?P<level>\w+)\s*ESC\[[\d;]*m \| (?P<log_message>.*)'

print("Testing current regex pattern...")
for i, line in enumerate(lines, 1):
    print(f"\nLine {i}: {repr(line)}")
    match = re.search(current_regex, line)
    if match:
        print(f"✅ MATCH: {match.groupdict()}")
    else:
        print("❌ NO MATCH")
        
        # Try to identify the actual pattern
        if '|' in line:
            parts = line.split('|')
            print(f"   Line has {len(parts)} parts separated by '|'")
            for j, part in enumerate(parts):
                print(f"   Part {j}: {repr(part.strip())}")
EOF

echo -e "\n${BLUE}9. Recommendations:${NC}"
echo "==================="
echo "Based on the analysis above:"
echo "1. If the regex doesn't match, we need to update the pattern"
echo "2. If permissions are wrong, fix with: sudo chmod 644 $LOG_FILE"
echo "3. If service is down, restart with: sudo systemctl restart google-cloud-ops-agent"
echo "4. If GCP project/API issues, configure gcloud and enable logging API"
echo "5. Check Google Cloud Console > Logging > Logs Explorer for entries"

echo -e "\n${YELLOW}To fix regex issues, run: python3 test_log_parser.py${NC}"
echo -e "${YELLOW}To monitor in real-time: sudo journalctl -u google-cloud-ops-agent -f${NC}"
