# Google Cloud Operations Agent Setup for Bittensor Logs

This repository contains configuration and scripts to set up Google Cloud Operations Agent to parse and extract structured information from Bittensor log files that contain ANSI color codes.

## Log Format

Your Bittensor logs follow this format:
```
ESC[34m2024-06-10 10:46:54.593ESC[0m | ESC[1m      INFO      ESC[0m | Setting up bittensor objects.
```

Where:
- `ESC[34m` and `ESC[0m` are ANSI color escape sequences
- `2024-06-10 10:46:54.593` is the timestamp
- `INFO` is the log level
- `Setting up bittensor objects.` is the log message
- `|` characters separate the fields

## Files Overview

### Configuration Files
- **`ops-agent-config.yaml`** - Basic Operations Agent configuration
- **`ops-agent-advanced-config.yaml`** - Advanced configuration with better ANSI handling
- **`sample-bittensor.log`** - Sample log file for testing

### Scripts
- **`deploy-ops-agent.sh`** - Automated deployment script
- **`test_log_parser.py`** - Python script to test log parsing

## Quick Start

### 1. Prerequisites
- Google Cloud VM instance
- Appropriate IAM permissions for Cloud Logging
- Root access to install the Operations Agent

### 2. Deploy Operations Agent

Make the deployment script executable and run it:
```bash
chmod +x deploy-ops-agent.sh
sudo ./deploy-ops-agent.sh
```

### 3. Test Log Parsing

Test the configuration with the sample log:
```bash
python3 test_log_parser.py
```

### 4. Copy Your Log Files

Copy your actual log files to the monitored directory:
```bash
sudo mkdir -p /var/log/bittensor
sudo cp your-log-file.log /var/log/bittensor/
```

## Configuration Details

### Log Parsing Strategy

The configuration uses a multi-step approach:

1. **ANSI Code Handling**: Regex patterns that match ANSI escape sequences
2. **Field Extraction**: Parse timestamp, log level, and message content
3. **Timestamp Parsing**: Convert to proper timestamp format
4. **Severity Mapping**: Map log levels to Google Cloud severity levels
5. **Label Enrichment**: Add custom labels for filtering and organization

### Regex Pattern

The main regex pattern used:
```regex
\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<level>\w+)\s*\x1b\[[\d;]*m \| (?P<message_content>.*?)(?:\x1b\[[\d;]*m)*$
```

### Extracted Fields

- **timestamp**: Original timestamp from log
- **level**: Log level (INFO, ERROR, WARN, DEBUG)
- **severity**: Google Cloud severity mapping
- **message**: Clean message content without ANSI codes
- **labels**: Custom labels for organization

## Manual Installation

If you prefer manual installation:

### 1. Install Operations Agent
```bash
curl -sSO https://dl.google.com/cloudagents/add-google-cloud-ops-agent-repo.sh
sudo bash add-google-cloud-ops-agent-repo.sh --also-install
```

### 2. Deploy Configuration
```bash
sudo cp ops-agent-advanced-config.yaml /etc/google-cloud-ops-agent/config.yaml
```

### 3. Restart Service
```bash
sudo systemctl restart google-cloud-ops-agent
```

## Monitoring and Troubleshooting

### Check Service Status
```bash
sudo systemctl status google-cloud-ops-agent
```

### View Service Logs
```bash
sudo journalctl -u google-cloud-ops-agent -f
```

### Test Configuration
```bash
# Validate YAML syntax
python3 -c "import yaml; yaml.safe_load(open('/etc/google-cloud-ops-agent/config.yaml'))"

# Test log parsing
python3 test_log_parser.py
```

### Common Issues

1. **ANSI Codes Not Parsed**: Ensure regex patterns handle `\x1b` escape sequences
2. **Timestamp Format**: Verify timestamp format matches `%Y-%m-%d %H:%M:%S.%f`
3. **File Permissions**: Ensure Operations Agent can read log files
4. **Path Configuration**: Update `include_paths` to match your log file locations

## Google Cloud Console

After successful deployment, you can:

1. View logs in Google Cloud Console → Logging
2. Filter by labels: `application="bittensor"`
3. Create log-based metrics
4. Set up alerting policies
5. Export logs to BigQuery for analysis

## Customization

### Adding New Log Paths
Edit the configuration file and add paths to `include_paths`:
```yaml
include_paths:
  - /var/log/bittensor/*.log
  - /home/<USER>/custom-logs/*.log
```

### Custom Labels
Add custom labels in the `label_enricher` processor:
```yaml
labels.environment:
  static_value: "production"
labels.region:
  static_value: "us-central1"
```

### Different Log Levels
Update the severity mapping in `test_log_parser.py` if you have custom log levels.

## Support

For issues with:
- **Google Cloud Operations Agent**: Check [official documentation](https://cloud.google.com/logging/docs/agent)
- **Configuration**: Review the YAML syntax and regex patterns
- **Log Parsing**: Run the test script and check output

## License

This configuration is provided as-is for educational and operational purposes.
